<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok Guessing Game</title>

    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: rgba(15, 15, 18, 1);
            color: white;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            gap: 10px;
            padding: 10px;
            max-width: 800px;
            margin: 0 auto;
        }

        .top-panel {
            display: flex;
            gap: 10px;
        }

        .left-panel {
            width: 250px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .canvas-panel {
            width: 500px;
            height: 500px;
            margin: 0 auto;
        }

        .panel {
            background-color: rgba(32, 28, 28, 1);
            border-radius: 12px;
            padding: 15px;
        }

        .word-input {
            background-color: transparent;
            border: 2px solid rgba(64, 64, 64, 1);
            border-radius: 8px;
            padding: 10px;
            color: white;
            font-size: 16px;
            width: 100%;
            box-sizing: border-box;
        }

        .word-input:focus {
            outline: none;
            border-color: rgba(256, 64, 88, 1);
        }

        .word-display {
            background-color: rgba(64, 64, 64, 1);
            border-radius: 8px;
            padding: 10px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            color: white;
        }

        .ranking-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 10px;
        }

        .ranking-item {
            background-color: rgba(64, 64, 64, 1);
            border-radius: 8px;
            padding: 10px;
            font-size: 14px;
            color: white;
        }

        .color-picker-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            position: relative;
        }

        .color-wheel {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            cursor: crosshair;
            position: relative;
            border: 3px solid rgba(64, 64, 64, 1);
        }

        .color-preview {
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
            pointer-events: none;
            z-index: 100;
            display: none;
        }

        .thickness-slider {
            width: 100%;
            -webkit-appearance: none;
            height: 8px;
            border-radius: 4px;
            background: rgba(64, 64, 64, 1);
            outline: none;
        }

        .thickness-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(256, 64, 88, 1);
            cursor: pointer;
        }

        .mode-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .mode-button {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background-color: rgba(32, 28, 28, 1);
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .mode-button.active {
            background-color: rgba(256, 64, 88, 1);
        }

        .clear-button, .reset-button {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background-color: rgba(64, 64, 64, 1);
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            margin-top: 15px;
            transition: background-color 0.2s;
        }

        .clear-button:hover, .reset-button:hover {
            background-color: rgba(80, 80, 80, 1);
        }

        .canvas-container {
            width: 500px;
            height: 500px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            border: 2px solid rgba(64, 64, 64, 1);
        }

        #drawingCanvas {
            width: 500px;
            height: 500px;
            cursor: crosshair;
        }

        .brush-preview {
            position: absolute;
            border: 2px solid rgba(128, 128, 128, 0.5);
            border-radius: 50%;
            pointer-events: none;
            z-index: 10;
            display: none;
        }

        .thickness-display {
            text-align: center;
            font-size: 14px;
            color: rgba(200, 200, 200, 1);
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <!-- Top Panel -->
        <div class="top-panel">
            <!-- Left Panel -->
            <div class="left-panel">
                <!-- Combined Word and Ranking Panel -->
                <div class="panel" style="flex: 1;">
                    <input
                        v-if="!wordSet"
                        v-model="currentWord"
                        @keyup.enter="setWord"
                        class="word-input"
                        placeholder="Enter word to guess..."
                        type="text"
                    />
                    <div v-else class="word-display">{{ currentWord }}</div>

                    <div class="ranking-list">
                        <div class="ranking-item">
                            <strong>1st:</strong> {{ winners[0] || 'Waiting...' }}
                        </div>
                        <div class="ranking-item">
                            <strong>2nd:</strong> {{ winners[1] || 'Waiting...' }}
                        </div>
                        <div class="ranking-item">
                            <strong>3rd:</strong> {{ winners[2] || 'Waiting...' }}
                        </div>
                    </div>
                </div>

                <!-- Reset Button -->
                <button @click="resetGame" class="reset-button">
                    Reset Canvas, Guesser Ranking and Word Input
                </button>
            </div>

            <!-- Right Panel -->
            <div class="right-panel">
                <!-- Color Picker Panel -->
                <div class="panel">
                    <div class="color-picker-container">
                        <canvas
                            ref="colorWheel"
                            class="color-wheel"
                            width="180"
                            height="180"
                            @mousedown="startColorSelection"
                            @mousemove="updateColorSelection"
                            @mouseup="endColorSelection"
                            @mouseleave="endColorSelection"
                        ></canvas>
                        <div ref="colorPreview" class="color-preview"></div>

                        <div style="margin-top: 15px; width: 100%;">
                            <label style="font-size: 14px; margin-bottom: 8px; display: block;">Thickness: {{ brushSize }}px</label>
                            <input
                                v-model="brushSize"
                                type="range"
                                min="2"
                                max="50"
                                class="thickness-slider"
                            />
                        </div>
                    </div>
                </div>

                <!-- Mode Buttons -->
                <div class="panel">
                    <div class="mode-buttons">
                        <button
                            @click="setMode('paint')"
                            :class="['mode-button', { active: currentMode === 'paint' }]"
                        >
                            Paint Mode
                        </button>
                        <button
                            @click="setMode('erase')"
                            :class="['mode-button', { active: currentMode === 'erase' }]"
                        >
                            Erase Mode
                        </button>
                    </div>
                </div>

                <!-- Clear Canvas Button -->
                <div class="panel">
                    <button @click="clearCanvas" class="clear-button">
                        Clear Canvas
                    </button>
                </div>
            </div>
        </div>

        <!-- Canvas Panel -->
        <div class="canvas-panel">
            <div class="canvas-container">
                <canvas
                    ref="drawingCanvas"
                    id="drawingCanvas"
                    width="500"
                    height="500"
                    @mousedown="startDrawing"
                    @mousemove="draw"
                    @mouseup="stopDrawing"
                    @mousemove="updateBrushPreview"
                ></canvas>
                <div
                    ref="brushPreview"
                    class="brush-preview"
                ></div>
            </div>
        </div>

        <!-- Connection Status (Hidden but functional) -->
        <div style="position: absolute; top: 10px; right: 10px; font-size: 12px; opacity: 0.7;">
            Status:
            <span :style="{ color: status === 'Connected' ? '#10b981' : status === 'Disconnected' ? '#ef4444' : '#f59e0b' }">
                {{ status }}
            </span>
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        const app = createApp({
            data() {
                return {
                    status: 'Disconnected',
                    currentWord: '',
                    wordSet: false,
                    winners: ['', '', ''],
                    currentMode: 'paint',
                    brushSize: 10,
                    currentColor: '#ff0000',
                    isDrawing: false,
                    canvas: null,
                    ctx: null,
                    colorWheelCanvas: null,
                    colorWheelCtx: null
                };
            },
            mounted() {
                this.initializeCanvas();
                this.initializeColorWheel();
                this.setupWebSocket();
            },
            methods: {
                initializeCanvas() {
                    this.canvas = this.$refs.drawingCanvas;
                    this.ctx = this.canvas.getContext('2d');

                    // Canvas is now fixed at 500x500
                    this.canvas.width = 500;
                    this.canvas.height = 500;

                    // Set canvas background to transparent
                    this.ctx.globalCompositeOperation = 'source-over';

                    // Add global mouse event listeners to handle drawing state
                    document.addEventListener('mouseup', () => {
                        if (this.isDrawing) {
                            this.stopDrawing();
                        }
                    });

                    document.addEventListener('mousemove', (event) => {
                        if (this.isDrawing && event.target === this.canvas) {
                            this.draw(event);
                        }
                    });
                },

                initializeColorWheel() {
                    this.colorWheelCanvas = this.$refs.colorWheel;
                    this.colorWheelCtx = this.colorWheelCanvas.getContext('2d');
                    this.drawColorWheel();
                },

                drawColorWheel() {
                    const canvas = this.colorWheelCanvas;
                    const ctx = this.colorWheelCtx;
                    const centerX = canvas.width / 2;
                    const centerY = canvas.height / 2;
                    const radius = Math.min(centerX, centerY) - 5;

                    // Clear canvas
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    // Draw color wheel
                    for (let angle = 0; angle < 360; angle++) {
                        const startAngle = (angle - 1) * Math.PI / 180;
                        const endAngle = angle * Math.PI / 180;

                        ctx.beginPath();
                        ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                        ctx.lineWidth = radius;
                        ctx.strokeStyle = `hsl(${angle}, 100%, 50%)`;
                        ctx.stroke();
                    }

                    // Draw inner circle for brightness/saturation
                    const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius * 0.7);
                    gradient.addColorStop(0, 'white');
                    gradient.addColorStop(1, 'transparent');

                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius * 0.7, 0, 2 * Math.PI);
                    ctx.fillStyle = gradient;
                    ctx.fill();
                },

                selectColor(event) {
                    const rect = this.colorWheelCanvas.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    const y = event.clientY - rect.top;

                    const centerX = this.colorWheelCanvas.width / 2;
                    const centerY = this.colorWheelCanvas.height / 2;

                    const dx = x - centerX;
                    const dy = y - centerY;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    const radius = Math.min(centerX, centerY) - 5;

                    if (distance <= radius) {
                        const angle = Math.atan2(dy, dx) * 180 / Math.PI;
                        const hue = (angle + 360) % 360;
                        const saturation = Math.min(distance / (radius * 0.7), 1) * 100;
                        const lightness = 50;

                        this.currentColor = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
                    }
                },

                updateColorPreview(event) {
                    // Optional: Show color preview on hover
                },

                setMode(mode) {
                    this.currentMode = mode;
                },

                startDrawing(event) {
                    this.isDrawing = true;
                    const rect = this.canvas.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    const y = event.clientY - rect.top;

                    this.ctx.beginPath();
                    this.ctx.moveTo(x, y);
                },

                draw(event) {
                    if (!this.isDrawing) return;

                    const rect = this.canvas.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    const y = event.clientY - rect.top;

                    this.ctx.lineWidth = this.brushSize;
                    this.ctx.lineCap = 'round';
                    this.ctx.lineJoin = 'round';

                    if (this.currentMode === 'paint') {
                        this.ctx.globalCompositeOperation = 'source-over';
                        this.ctx.strokeStyle = this.currentColor;
                    } else {
                        this.ctx.globalCompositeOperation = 'destination-out';
                    }

                    this.ctx.lineTo(x, y);
                    this.ctx.stroke();
                    this.ctx.beginPath();
                    this.ctx.moveTo(x, y);
                },

                stopDrawing() {
                    this.isDrawing = false;
                    this.ctx.beginPath();
                },

                updateBrushPreview(event) {
                    if (this.isDrawing) {
                        this.$refs.brushPreview.style.display = 'none';
                        return;
                    }

                    const rect = this.canvas.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    const y = event.clientY - rect.top;

                    const preview = this.$refs.brushPreview;
                    preview.style.display = 'block';
                    preview.style.left = (x - this.brushSize / 2) + 'px';
                    preview.style.top = (y - this.brushSize / 2) + 'px';
                    preview.style.width = this.brushSize + 'px';
                    preview.style.height = this.brushSize + 'px';
                },

                clearCanvas() {
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                },

                setWord() {
                    if (this.currentWord.trim()) {
                        this.wordSet = true;
                    }
                },

                resetGame() {
                    this.currentWord = '';
                    this.wordSet = false;
                    this.winners = ['', '', ''];
                    this.clearCanvas();
                },

                updateStatus(newStatus) {
                    this.status = newStatus;
                },

                handleChatMessage(data) {
                    // Handle chat messages for guessing game
                    if (this.wordSet && data.event === 'chat' && data.data && data.data.comment) {
                        const message = data.data.comment.toLowerCase().trim();
                        const word = this.currentWord.toLowerCase().trim();
                        const username = data.data.uniqueId || 'Anonymous';

                        if (message === word) {
                            // Check if user already won
                            if (!this.winners.includes(username)) {
                                // Find first empty slot
                                for (let i = 0; i < 3; i++) {
                                    if (!this.winners[i]) {
                                        this.winners[i] = username;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                },

                setupWebSocket() {
                    this.connect();
                },

                connect() {
                    if (this.websocket) return;

                    this.websocket = new WebSocket("ws://localhost:21213/");

                    this.websocket.onopen = () => {
                        this.updateStatus("Connected");
                    };

                    this.websocket.onclose = () => {
                        this.updateStatus("Disconnected");
                        this.websocket = null;
                        setTimeout(() => this.connect(), 1000);
                    };

                    this.websocket.onerror = () => {
                        this.updateStatus("Connection Failed");
                        this.websocket = null;
                        setTimeout(() => this.connect(), 1000);
                    };

                    this.websocket.onmessage = (event) => {
                        let parsedData = JSON.parse(event.data);
                        console.log("Data received", parsedData);
                        this.handleChatMessage(parsedData);
                    };
                }
            }
        }).mount('#app');
    </script>
</body>
</html>